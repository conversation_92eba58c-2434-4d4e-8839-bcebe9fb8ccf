# 静默打印服务 - 完整解决方案

## 🎯 功能特点

✅ **真正的静默打印** - 无需用户确认，直接打印  
✅ **自动安装依赖** - 自动下载安装Python和所需依赖  
✅ **打印机配置** - 支持选择和管理多台打印机  
✅ **Web API接口** - 提供HTTP API供浏览器扩展调用  
✅ **Windows服务** - 后台运行，开机自启  
✅ **管理界面** - 提供Web管理界面和桌面快捷方式  

## 🚀 一键安装（推荐）

### 方法1：PowerShell自动安装（推荐）
```powershell
# 右键点击 auto_install.ps1，选择"使用PowerShell运行"
# 或者以管理员身份运行PowerShell，然后执行：
powershell -ExecutionPolicy Bypass -File auto_install.ps1
```

### 方法2：批处理自动安装
```cmd
# 右键点击 auto_install.bat，选择"以管理员身份运行"
auto_install.bat
```

### 方法3：Python自动安装（如果已有Python）
```cmd
# 以管理员身份运行命令提示符，然后执行：
python setup.py
```

## 📋 安装选项对比

| 安装方式 | 自动下载Python | 中文支持 | 推荐度 |
|---------|---------------|---------|--------|
| auto_install.ps1 | ✅ | ✅ | ⭐⭐⭐⭐⭐ |
| auto_install.bat | ✅ | ⚠️ | ⭐⭐⭐⭐ |
| setup.py | ❌ | ✅ | ⭐⭐⭐ |
| 手动安装 | ❌ | ✅ | ⭐⭐ |

## 🛠️ 手动安装（备用方案）

如果自动安装失败，请按以下步骤手动安装：

### 1. 安装Python
- 下载：https://www.python.org/downloads/
- 安装时勾选"Add Python to PATH"

### 2. 安装依赖
```cmd
pip install pywin32 requests
```

### 3. 安装服务
```cmd
python install_service.py install
```

## 📖 使用方法

### 1. 通过浏览器扩展（推荐）
1. 重新加载Chrome扩展
2. 点击扩展图标
3. 选择打印机
4. 点击"打印当前页面"
5. **无需确认，直接静默打印！**

### 2. 通过Web API
```javascript
// 静默打印当前页面
fetch('http://localhost:8888/print', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    html: document.documentElement.outerHTML,
    printer: 'HP LaserJet Pro'
  })
});
```

### 3. 通过管理界面
- 双击桌面"静默打印管理器"快捷方式
- 或打开 `print_manager.html`

## 🔧 服务管理

```cmd
# 启动服务
python install_service.py start

# 停止服务
python install_service.py stop

# 重启服务
python install_service.py restart

# 卸载服务
python install_service.py uninstall
```

## 📊 API接口文档

### 基础信息
- **服务地址**: http://localhost:8888
- **支持CORS**: 是
- **数据格式**: JSON

### 接口列表

#### GET /status
获取服务状态
```json
{
  "success": true,
  "status": "running",
  "version": "1.0.0"
}
```

#### GET /printers
获取打印机列表
```json
{
  "success": true,
  "printers": [
    {
      "name": "HP LaserJet Pro",
      "server": "Local",
      "is_default": true,
      "status": "Ready"
    }
  ]
}
```

#### POST /print
打印HTML内容
```json
// 请求
{
  "html": "<html>...</html>",
  "printer": "HP LaserJet Pro",  // 可选，不指定则使用默认打印机
  "copies": 1                    // 可选，默认1份
}

// 响应
{
  "success": true,
  "message": "打印任务已提交"
}
```

#### POST /set-default-printer
设置默认打印机
```json
// 请求
{
  "printer": "HP LaserJet Pro"
}

// 响应
{
  "success": true,
  "message": "默认打印机已设置"
}
```

## 🔍 故障排除

### 安装问题
1. **权限不足**: 确保以管理员身份运行
2. **网络问题**: 检查网络连接，或使用手动安装
3. **防火墙**: 确保允许Python程序联网

### 服务问题
1. **服务无法启动**: 查看日志 `C:\temp\silent_print_service.log`
2. **端口被占用**: 修改 `SilentPrintService.py` 中的端口号
3. **打印失败**: 检查打印机状态和驱动

### 扩展问题
1. **无法连接服务**: 确认服务正在运行
2. **打印机列表为空**: 检查系统打印机安装
3. **仍弹出对话框**: 确认本地服务可用

## 🔒 安全说明

- 服务仅监听本地地址 (localhost:8888)
- 不接受外部网络连接
- 建议在企业内网环境使用
- 可通过防火墙进一步限制访问

## 📝 文件说明

| 文件 | 说明 |
|------|------|
| `auto_install.ps1` | PowerShell自动安装脚本（推荐） |
| `auto_install.bat` | 批处理自动安装脚本 |
| `setup.py` | Python自动安装脚本 |
| `SilentPrintService.py` | Windows服务主程序 |
| `install_service.py` | 服务管理脚本 |
| `print_manager.html` | Web管理界面 |
| `popup.html/js` | 浏览器扩展界面 |
| `content.js` | 浏览器扩展内容脚本 |
| `manifest.json` | 扩展配置文件 |

## 🎊 完成！

现在你拥有了一个完整的静默打印解决方案！

- **浏览器扩展**提供便捷的用户界面
- **Windows服务**实现真正的静默打印
- **Web管理界面**提供配置和测试功能
- **自动安装脚本**简化部署过程

享受真正的静默打印体验吧！🖨️✨
