@echo off
echo.
echo ========================================
echo        静默打印服务 - 一键安装脚本
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 错误：需要管理员权限！
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo [成功] 管理员权限验证通过
echo.

:: 检查Python是否已安装
python --version >nul 2>&1
if %errorLevel% equ 0 (
    echo [成功] 检测到已安装的Python
    for /f "tokens=*" %%i in ('python --version 2^>^&1') do echo       版本：%%i
    goto :install_dependencies
)

echo [信息] 未检测到Python，将自动下载安装
echo.

:: 下载并安装Python
echo [步骤 1/4] 正在下载Python安装程序...
echo 根据网络速度，这可能需要几分钟时间...

:: 使用PowerShell下载Python安装程序
powershell -Command "& {try { Write-Host '正在下载Python 3.11.7...'; Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe' -OutFile '%TEMP%\python-installer.exe' -UseBasicParsing; Write-Host '下载完成' } catch { Write-Host '下载失败:' $_.Exception.Message; exit 1 }}"

if %errorLevel% neq 0 (
    echo 错误：Python安装程序下载失败
    echo 请检查网络连接或手动安装Python
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

if not exist "%TEMP%\python-installer.exe" (
    echo 错误：下载后未找到Python安装程序
    echo 请手动安装Python：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [成功] Python安装程序下载完成
echo.

echo [步骤 2/4] 正在安装Python...
echo 静默安装中（可能需要3-5分钟）...
echo 请耐心等待，不要关闭此窗口...

:: 静默安装Python，包含所有必要选项
"%TEMP%\python-installer.exe" /quiet InstallAllUsers=1 PrependPath=1 Include_test=0 Include_pip=1 Include_tcltk=0 Include_launcher=1

:: 等待安装完成
echo 等待Python安装完成...
timeout /t 15 /nobreak >nul

:: 刷新环境变量
call :refresh_env

:: 验证Python安装
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo 错误：Python安装验证失败
    echo 安装可能仍在进行中或已失败
    echo 请稍等片刻后重新运行此脚本
    echo 或手动安装Python：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [成功] Python安装并验证完成
for /f "tokens=*" %%i in ('python --version 2^>^&1') do echo       版本：%%i

:: 清理安装文件
del "%TEMP%\python-installer.exe" >nul 2>&1

echo.

:install_dependencies

echo [步骤 3/4] 正在安装Python依赖包...

:: 首先升级pip
echo 正在升级pip到最新版本...
python -m pip install --upgrade pip --quiet

:: 安装必需的包
echo 正在安装pywin32...
python -m pip install pywin32 --quiet
if %errorLevel% neq 0 (
    echo 错误：pywin32安装失败！
    pause
    exit /b 1
)

echo 正在安装requests...
python -m pip install requests --quiet
if %errorLevel% neq 0 (
    echo 错误：requests安装失败！
    pause
    exit /b 1
)

echo [成功] 所有依赖包安装完成
echo.

echo [步骤 4/4] 正在安装Windows服务...
python install_service.py install
if %errorLevel% neq 0 (
    echo 错误：服务安装失败！
    pause
    exit /b 1
)

echo [成功] 服务安装完成
echo.

:: 创建桌面快捷方式
echo 正在创建桌面快捷方式...
powershell -Command "& {try { $WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\静默打印管理器.lnk'); $Shortcut.TargetPath = '%CD%\print_manager.html'; $Shortcut.WorkingDirectory = '%CD%'; $Shortcut.Description = '静默打印服务管理界面'; $Shortcut.Save(); Write-Host '桌面快捷方式创建成功' } catch { Write-Host '桌面快捷方式创建失败' }}"

echo.
echo ========================================
echo           安装成功完成！
echo ========================================
echo.
echo 服务信息：
echo    服务名称：SilentPrintService
echo    API地址：http://localhost:8888
echo    管理界面：print_manager.html
echo    桌面快捷方式：静默打印管理器
echo.
echo 下一步操作：
echo    1. 重新加载Chrome扩展
echo    2. 扩展会自动检测本地服务
echo    3. 选择打印机并测试打印
echo    4. 享受真正的静默打印！
echo.
echo 服务管理命令：
echo    python install_service.py start    - 启动服务
echo    python install_service.py stop     - 停止服务
echo    python install_service.py restart  - 重启服务
echo    python install_service.py uninstall - 卸载服务
echo.

:: 询问是否打开管理界面
set /p choice="是否立即打开管理界面？(Y/N): "
if /i "%choice%"=="Y" (
    start print_manager.html
)

echo.
echo 安装完成！现在可以享受静默打印了！
echo 按任意键退出...
pause >nul

goto :eof

:: 刷新环境变量的函数
:refresh_env
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set "SysPath=%%b"
for /f "tokens=2*" %%a in ('reg query "HKCU\Environment" /v PATH 2^>nul') do set "UserPath=%%b"
set "PATH=%SysPath%;%UserPath%"
goto :eof
