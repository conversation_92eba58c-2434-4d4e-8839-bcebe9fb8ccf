@echo off
echo.
echo ========================================
echo    Silent Print Service - Auto Installer
echo ========================================
echo.

:: Check admin privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Administrator privileges required!
    echo Please right-click this file and select "Run as administrator"
    pause
    exit /b 1
)

echo [OK] Administrator privileges verified
echo.

:: Check if Python is installed
python --version >nul 2>&1
if %errorLevel% equ 0 (
    echo [OK] Python is already installed
    for /f "tokens=*" %%i in ('python --version 2^>^&1') do echo       %%i
    goto :install_dependencies
)

echo [INFO] Python not found, will download and install automatically
echo.

:: Download and install Python
echo [STEP 1/4] Downloading Python installer...
echo This may take a few minutes depending on your internet connection...

:: Use PowerShell to download Python installer
powershell -Command "& {try { Write-Host 'Downloading Python 3.11.7...'; Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe' -OutFile '%TEMP%\python-installer.exe' -UseBasicParsing; Write-Host 'Download completed successfully' } catch { Write-Host 'Download failed:' $_.Exception.Message; exit 1 }}"

if %errorLevel% neq 0 (
    echo ERROR: Failed to download Python installer
    echo Please check your internet connection or manually install Python
    echo Download URL: https://www.python.org/downloads/
    pause
    exit /b 1
)

if not exist "%TEMP%\python-installer.exe" (
    echo ERROR: Python installer not found after download
    echo Please manually install Python from: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [OK] Python installer downloaded successfully
echo.

echo [STEP 2/4] Installing Python...
echo This will install Python silently (may take 3-5 minutes)...
echo Please wait, do not close this window...

:: Install Python silently with all required options
"%TEMP%\python-installer.exe" /quiet InstallAllUsers=1 PrependPath=1 Include_test=0 Include_pip=1 Include_tcltk=0 Include_launcher=1

:: Wait for installation to complete
echo Waiting for Python installation to complete...
timeout /t 15 /nobreak >nul

:: Refresh environment variables
call :refresh_env

:: Verify Python installation
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Python installation verification failed
    echo The installation may still be in progress or failed
    echo Please wait a moment and try running this script again
    echo Or manually install Python from: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [OK] Python installed and verified successfully
for /f "tokens=*" %%i in ('python --version 2^>^&1') do echo       %%i

:: Clean up installer
del "%TEMP%\python-installer.exe" >nul 2>&1

echo.

:install_dependencies

echo [STEP 3/4] Installing Python dependencies...

:: Upgrade pip first
echo Upgrading pip to latest version...
python -m pip install --upgrade pip --quiet

:: Install required packages
echo Installing pywin32...
python -m pip install pywin32 --quiet
if %errorLevel% neq 0 (
    echo ERROR: Failed to install pywin32!
    pause
    exit /b 1
)

echo Installing requests...
python -m pip install requests --quiet
if %errorLevel% neq 0 (
    echo ERROR: Failed to install requests!
    pause
    exit /b 1
)

echo [OK] All dependencies installed successfully
echo.

echo [STEP 4/4] Installing Windows service...
python install_service.py install
if %errorLevel% neq 0 (
    echo ERROR: Failed to install service!
    pause
    exit /b 1
)

echo [OK] Service installed successfully
echo.

:: Create desktop shortcut
echo Creating desktop shortcut...
powershell -Command "& {try { $WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Silent Print Manager.lnk'); $Shortcut.TargetPath = '%CD%\print_manager.html'; $Shortcut.WorkingDirectory = '%CD%'; $Shortcut.Description = 'Silent Print Service Management Interface'; $Shortcut.Save(); Write-Host 'Desktop shortcut created successfully' } catch { Write-Host 'Failed to create desktop shortcut' }}"

echo.
echo ========================================
echo    INSTALLATION COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo Service Information:
echo    Service Name: SilentPrintService
echo    API Address: http://localhost:8888
echo    Management UI: print_manager.html
echo    Desktop Shortcut: Silent Print Manager
echo.
echo What's Next:
echo    1. Reload your Chrome extension
echo    2. Extension will auto-detect the local service
echo    3. Select a printer and test printing
echo    4. Enjoy TRUE SILENT PRINTING!
echo.
echo Service Management Commands:
echo    python install_service.py start    - Start service
echo    python install_service.py stop     - Stop service
echo    python install_service.py restart  - Restart service
echo    python install_service.py uninstall - Remove service
echo.

:: Ask to open management interface
set /p choice="Open management interface now? (Y/N): "
if /i "%choice%"=="Y" (
    start print_manager.html
)

echo.
echo Installation completed! You can now enjoy silent printing!
echo Press any key to exit...
pause >nul

goto :eof

:: Function to refresh environment variables
:refresh_env
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set "SysPath=%%b"
for /f "tokens=2*" %%a in ('reg query "HKCU\Environment" /v PATH 2^>nul') do set "UserPath=%%b"
set "PATH=%SysPath%;%UserPath%"
goto :eof
