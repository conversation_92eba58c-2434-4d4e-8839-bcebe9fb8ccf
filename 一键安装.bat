@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    静默打印服务 - 一键安装脚本
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 需要管理员权限！
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo ✅ 管理员权限检查通过
echo.

:: 检查Python
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 未找到Python！
    echo 请先安装Python 3.7+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

:: 安装依赖
echo 🔄 正在安装Python依赖...
pip install pywin32
if %errorLevel% neq 0 (
    echo ❌ 依赖安装失败！
    pause
    exit /b 1
)

echo ✅ 依赖安装成功
echo.

:: 安装服务
echo 🔄 正在安装Windows服务...
python install_service.py install
if %errorLevel% neq 0 (
    echo ❌ 服务安装失败！
    pause
    exit /b 1
)

echo.
echo 🎉 安装完成！
echo.
echo 📋 服务信息:
echo    服务名称: SilentPrintService
echo    API地址: http://localhost:8888
echo    管理界面: print_manager.html
echo.
echo 📖 使用说明:
echo    1. 重新加载Chrome扩展
echo    2. 扩展会自动检测本地服务
echo    3. 选择打印机并测试打印
echo    4. 享受真正的静默打印！
echo.

:: 询问是否打开管理界面
set /p choice="是否打开管理界面？(Y/N): "
if /i "%choice%"=="Y" (
    start print_manager.html
)

echo.
echo 按任意键退出...
pause >nul
