@echo off
echo.
echo ========================================
echo    Silent Print Service - Install Script
echo ========================================
echo.

:: Check admin privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Administrator privileges required!
    echo Please right-click this file and select "Run as administrator"
    pause
    exit /b 1
)

echo OK: Administrator privileges verified
echo.

:: Check Python
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Python not found!
    echo Please install Python 3.7+ first
    echo Download: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo OK: Python environment verified
echo.

:: Install dependencies
echo Installing Python dependencies...
pip install pywin32
if %errorLevel% neq 0 (
    echo ERROR: Failed to install dependencies!
    pause
    exit /b 1
)

echo OK: Dependencies installed successfully
echo.

:: Install service
echo Installing Windows service...
python install_service.py install
if %errorLevel% neq 0 (
    echo ERROR: Failed to install service!
    pause
    exit /b 1
)

echo.
echo SUCCESS: Installation completed!
echo.
echo Service Information:
echo    Service Name: SilentPrintService
echo    API Address: http://localhost:8888
echo    Management UI: print_manager.html
echo.
echo Usage Instructions:
echo    1. Reload Chrome extension
echo    2. Extension will auto-detect local service
echo    3. Select printer and test printing
echo    4. Enjoy true silent printing!
echo.

:: Ask to open management interface
set /p choice="Open management interface? (Y/N): "
if /i "%choice%"=="Y" (
    start print_manager.html
)

echo.
echo Press any key to exit...
pause >nul
