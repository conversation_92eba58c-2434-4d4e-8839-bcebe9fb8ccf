# 静默打印服务 - 故障排除指南

## 🔧 常见问题解决方案

### 问题1：找不到 install_service.py 文件
```
python: can't open file 'install_service.py': [Errno 2] No such file or directory
```

**原因**：脚本在错误的目录下运行

**解决方案**：
1. 确保所有文件都在同一目录下
2. 运行 `test_path.bat` 检查文件状态
3. 使用 `简单安装.bat` 替代 `一键安装.bat`

### 问题2：Python未安装
```
'python' 不是内部或外部命令
```

**解决方案**：
1. 使用 `一键安装.bat` 自动安装Python
2. 或手动安装Python：https://www.python.org/downloads/
3. 安装时勾选"Add Python to PATH"

### 问题3：权限不足
```
ERROR: Administrator privileges required!
```

**解决方案**：
1. 右键点击批处理文件
2. 选择"以管理员身份运行"

### 问题4：依赖安装失败
```
ERROR: Failed to install pywin32!
```

**解决方案**：
1. 检查网络连接
2. 手动安装：`pip install pywin32 requests`
3. 使用国内镜像：`pip install -i https://pypi.tuna.tsinghua.edu.cn/simple pywin32`

### 问题5：服务安装失败
```
ERROR: Failed to install service!
```

**解决方案**：
1. 确保以管理员权限运行
2. 检查Windows服务是否支持
3. 查看详细错误信息
4. 重启计算机后重试

## 🛠️ 手动安装步骤

如果自动安装失败，请按以下步骤手动安装：

### 1. 检查环境
```cmd
# 检查Python
python --version

# 检查pip
pip --version

# 检查文件
dir *.py *.html *.json
```

### 2. 安装依赖
```cmd
# 升级pip
python -m pip install --upgrade pip

# 安装依赖
pip install pywin32 requests
```

### 3. 安装服务
```cmd
# 切换到项目目录
cd /d "D:\Demo\cxr"

# 安装服务
python install_service.py install
```

### 4. 启动服务
```cmd
python install_service.py start
```

### 5. 验证安装
```cmd
# 检查服务状态
python install_service.py status

# 或访问
http://localhost:8888/status
```

## 📋 文件清单

确保以下文件都在同一目录下：

**必需文件**：
- ✅ `SilentPrintService.py` - 服务主程序
- ✅ `install_service.py` - 安装脚本
- ✅ `print_manager.html` - 管理界面
- ✅ `manifest.json` - 扩展配置
- ✅ `popup.html` - 扩展界面
- ✅ `popup.js` - 扩展脚本
- ✅ `content.js` - 内容脚本

**安装脚本**：
- ✅ `一键安装.bat` - 全自动安装（推荐）
- ✅ `简单安装.bat` - 简化安装（备用）
- ✅ `test_path.bat` - 路径测试

## 🔍 调试方法

### 1. 检查文件路径
```cmd
# 运行测试脚本
test_path.bat
```

### 2. 查看服务日志
```
日志文件：C:\temp\silent_print_service.log
```

### 3. 检查Windows服务
```cmd
# 查看服务状态
sc query SilentPrintService

# 启动服务
sc start SilentPrintService

# 停止服务
sc stop SilentPrintService
```

### 4. 测试API
```cmd
# 使用curl测试
curl http://localhost:8888/status

# 或在浏览器中访问
http://localhost:8888/status
```

## 📞 获取帮助

如果问题仍未解决：

1. **运行诊断**：`test_path.bat`
2. **查看日志**：`C:\temp\silent_print_service.log`
3. **检查端口**：确保8888端口未被占用
4. **重启系统**：有时需要重启后生效
5. **使用简化版本**：尝试 `简单安装.bat`

## ✅ 成功标志

安装成功后应该看到：
- ✅ 服务运行：`python install_service.py status`
- ✅ API响应：访问 `http://localhost:8888/status`
- ✅ 管理界面：打开 `print_manager.html`
- ✅ 扩展连接：浏览器扩展显示"本地服务已连接"
