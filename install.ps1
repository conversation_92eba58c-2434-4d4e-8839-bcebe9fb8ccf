# 静默打印服务安装脚本 (PowerShell)
# Silent Print Service Installer

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    静默打印服务 - 安装脚本" -ForegroundColor Cyan
Write-Host "    Silent Print Service Installer" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ 需要管理员权限！" -ForegroundColor Red
    Write-Host "ERROR: Administrator privileges required!" -ForegroundColor Red
    Write-Host "请右键点击此文件，选择'以管理员身份运行'" -ForegroundColor Yellow
    Write-Host "Please right-click this file and select 'Run as administrator'" -ForegroundColor Yellow
    Read-Host "按回车键退出 (Press Enter to exit)"
    exit 1
}

Write-Host "✅ 管理员权限检查通过" -ForegroundColor Green
Write-Host "[OK] Administrator privileges verified" -ForegroundColor Green
Write-Host ""

# 检查Python
try {
    $pythonVersion = python --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Python not found"
    }
    Write-Host "✅ Python环境检查通过: $pythonVersion" -ForegroundColor Green
    Write-Host "[OK] Python environment verified: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 未找到Python！" -ForegroundColor Red
    Write-Host "ERROR: Python not found!" -ForegroundColor Red
    Write-Host "请先安装Python 3.7+" -ForegroundColor Yellow
    Write-Host "Please install Python 3.7+ first" -ForegroundColor Yellow
    Write-Host "下载地址 (Download): https://www.python.org/downloads/" -ForegroundColor Yellow
    Read-Host "按回车键退出 (Press Enter to exit)"
    exit 1
}

Write-Host ""

# 安装依赖
Write-Host "🔄 正在安装Python依赖..." -ForegroundColor Yellow
Write-Host "Installing Python dependencies..." -ForegroundColor Yellow

try {
    pip install pywin32
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to install dependencies"
    }
    Write-Host "✅ 依赖安装成功" -ForegroundColor Green
    Write-Host "[OK] Dependencies installed successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ 依赖安装失败！" -ForegroundColor Red
    Write-Host "ERROR: Failed to install dependencies!" -ForegroundColor Red
    Read-Host "按回车键退出 (Press Enter to exit)"
    exit 1
}

Write-Host ""

# 安装服务
Write-Host "🔄 正在安装Windows服务..." -ForegroundColor Yellow
Write-Host "Installing Windows service..." -ForegroundColor Yellow

try {
    python install_service.py install
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to install service"
    }
    Write-Host "✅ 服务安装成功" -ForegroundColor Green
    Write-Host "[OK] Service installed successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ 服务安装失败！" -ForegroundColor Red
    Write-Host "ERROR: Failed to install service!" -ForegroundColor Red
    Read-Host "按回车键退出 (Press Enter to exit)"
    exit 1
}

Write-Host ""
Write-Host "🎉 安装完成！" -ForegroundColor Green
Write-Host "SUCCESS: Installation completed!" -ForegroundColor Green
Write-Host ""

Write-Host "📋 服务信息 (Service Information):" -ForegroundColor Cyan
Write-Host "   服务名称 (Service Name): SilentPrintService" -ForegroundColor White
Write-Host "   API地址 (API Address): http://localhost:8888" -ForegroundColor White
Write-Host "   管理界面 (Management UI): print_manager.html" -ForegroundColor White
Write-Host ""

Write-Host "📖 使用说明 (Usage Instructions):" -ForegroundColor Cyan
Write-Host "   1. 重新加载Chrome扩展 (Reload Chrome extension)" -ForegroundColor White
Write-Host "   2. 扩展会自动检测本地服务 (Extension will auto-detect local service)" -ForegroundColor White
Write-Host "   3. 选择打印机并测试打印 (Select printer and test printing)" -ForegroundColor White
Write-Host "   4. 享受真正的静默打印！(Enjoy true silent printing!)" -ForegroundColor White
Write-Host ""

# 询问是否打开管理界面
$choice = Read-Host "是否打开管理界面？(Open management interface?) (Y/N)"
if ($choice -eq "Y" -or $choice -eq "y") {
    Start-Process "print_manager.html"
}

Write-Host ""
Write-Host "安装完成！按回车键退出..." -ForegroundColor Green
Write-Host "Installation completed! Press Enter to exit..." -ForegroundColor Green
Read-Host
