@echo off
echo 测试脚本路径和文件检查
echo ========================
echo.

:: 切换到脚本所在目录
cd /d "%~dp0"

echo 脚本所在目录：%~dp0
echo 当前工作目录：%CD%
echo.

echo 检查必要文件：
if exist "install_service.py" (
    echo ✅ install_service.py - 存在
) else (
    echo ❌ install_service.py - 不存在
)

if exist "SilentPrintService.py" (
    echo ✅ SilentPrintService.py - 存在
) else (
    echo ❌ SilentPrintService.py - 不存在
)

if exist "print_manager.html" (
    echo ✅ print_manager.html - 存在
) else (
    echo ❌ print_manager.html - 不存在
)

if exist "popup.html" (
    echo ✅ popup.html - 存在
) else (
    echo ❌ popup.html - 不存在
)

if exist "manifest.json" (
    echo ✅ manifest.json - 存在
) else (
    echo ❌ manifest.json - 不存在
)

echo.
echo 目录中的所有文件：
dir /b *.py *.html *.json *.bat

echo.
echo 测试Python调用：
python --version
if %errorLevel% equ 0 (
    echo ✅ Python 可用
) else (
    echo ❌ Python 不可用
)

echo.
pause
