<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>打印机API测试</title>
</head>
<body>
    <h1>打印机API测试页面</h1>
    <button onclick="testPrinters()">测试获取打印机</button>
    <div id="result"></div>
    
    <script>
        function testPrinters() {
            const resultDiv = document.getElementById('result');
            
            if (typeof chrome !== 'undefined' && chrome.printing) {
                chrome.printing.getPrinters().then(printers => {
                    resultDiv.innerHTML = '<h3>找到的打印机:</h3>' + 
                        printers.map(p => `<p>${p.name} (ID: ${p.id})</p>`).join('');
                }).catch(error => {
                    resultDiv.innerHTML = '<p style="color: red;">错误: ' + error.message + '</p>';
                });
            } else {
                resultDiv.innerHTML = '<p style="color: orange;">Chrome打印API不可用</p>';
            }
        }
        
        // 页面加载时自动测试
        window.onload = testPrinters;
    </script>
</body>
</html>
