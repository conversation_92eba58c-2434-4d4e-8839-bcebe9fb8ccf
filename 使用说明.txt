静默打印服务 - 使用说明
========================

🚀 一键安装（推荐）
==================
右键点击 "一键安装.bat"，选择 "以管理员身份运行"

✨ 全中文界面，自动安装功能：
- ✅ 自动检测Python安装状态
- ✅ 自动下载Python 3.11.7（如需要）
- ✅ 自动安装所有依赖包
- ✅ 自动安装Windows服务
- ✅ 自动创建桌面快捷方式"静默打印管理器"
- ✅ 完全中文提示，无需手动操作

📋 安装过程显示：
========================================
        静默打印服务 - 一键安装脚本
========================================

[成功] 管理员权限验证通过
[信息] 未检测到Python，将自动下载安装
[步骤 1/4] 正在下载Python安装程序...
[步骤 2/4] 正在安装Python...
[步骤 3/4] 正在安装Python依赖包...
[步骤 4/4] 正在安装Windows服务...

========================================
           安装成功完成！
========================================

🎯 使用方法：
============

方法1：浏览器扩展（推荐）
- 重新加载Chrome扩展
- 点击扩展图标
- 选择打印机
- 点击"打印当前页面"
- 🎉 无需确认，直接静默打印！

方法2：管理界面
- 双击桌面"Silent Print Manager"快捷方式
- 或打开 print_manager.html
- 测试打印功能
- 管理打印机设置

方法3：Web API调用
- 访问 http://localhost:8888
- 使用API接口进行打印

🔧 服务管理：
============
启动服务：python install_service.py start
停止服务：python install_service.py stop
重启服务：python install_service.py restart
卸载服务：python install_service.py uninstall

🔍 故障排除：
============
1. 如果安装失败，请确保：
   - 以管理员身份运行
   - 网络连接正常
   - 防火墙允许下载

2. 如果服务无法启动：
   - 查看日志：C:\temp\silent_print_service.log
   - 检查端口8888是否被占用

3. 如果扩展无法连接：
   - 确认服务正在运行
   - 重新加载浏览器扩展

🎊 完成！
========
现在你拥有了真正的静默打印功能！
享受无需确认的打印体验吧！🖨️✨
