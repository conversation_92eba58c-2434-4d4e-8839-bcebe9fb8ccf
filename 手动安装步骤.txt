静默打印服务 - 手动安装步骤
=====================================

如果自动安装脚本出现编码问题，请按以下步骤手动安装：

1. 检查管理员权限
   - 以管理员身份打开命令提示符
   - 方法：按Win+R，输入cmd，按Ctrl+Shift+Enter

2. 安装Python依赖
   在命令提示符中执行：
   pip install pywin32

3. 安装Windows服务
   在项目目录中执行：
   python install_service.py install

4. 验证安装
   检查服务是否运行：
   python install_service.py start

5. 测试服务
   打开浏览器访问：http://localhost:8888/status
   或者打开 print_manager.html

6. 配置浏览器扩展
   - 重新加载Chrome扩展
   - 扩展会自动检测本地服务

常用命令：
=========
启动服务：python install_service.py start
停止服务：python install_service.py stop
重启服务：python install_service.py restart
卸载服务：python install_service.py uninstall

故障排除：
=========
1. 如果出现编码错误，请使用PowerShell：
   powershell -ExecutionPolicy Bypass -File install.ps1

2. 如果服务无法启动，检查日志：
   C:\temp\silent_print_service.log

3. 如果端口被占用，修改SilentPrintService.py中的端口号

4. 确保防火墙允许localhost:8888访问
