@echo off
echo.
echo ========================================
echo    Silent Print Service - Auto Installer
echo ========================================
echo.

:: Check admin privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Administrator privileges required!
    echo Please right-click this file and select "Run as administrator"
    pause
    exit /b 1
)

echo [OK] Administrator privileges verified
echo.

:: Check if Python is installed
python --version >nul 2>&1
if %errorLevel% equ 0 (
    echo [OK] Python is already installed
    goto :install_dependencies
)

echo [INFO] Python not found, downloading and installing...
echo.

:: Download Python installer
echo Downloading Python installer...
powershell -Command "& {Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe' -OutFile '%TEMP%\python-installer.exe'}"

if not exist "%TEMP%\python-installer.exe" (
    echo ERROR: Failed to download Python installer
    echo Please manually install Python from: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [OK] Python installer downloaded
echo.

:: Install Python silently
echo Installing Python (this may take a few minutes)...
"%TEMP%\python-installer.exe" /quiet InstallAllUsers=1 PrependPath=1 Include_test=0 Include_pip=1

:: Wait for installation to complete
timeout /t 10 /nobreak >nul

:: Refresh environment variables
call :refresh_env

:: Verify Python installation
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Python installation failed
    echo Please manually install Python from: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [OK] Python installed successfully
echo.

:: Clean up installer
del "%TEMP%\python-installer.exe" >nul 2>&1

:install_dependencies
:: Upgrade pip
echo Upgrading pip...
python -m pip install --upgrade pip --quiet

:: Install dependencies
echo Installing Python dependencies...
python -m pip install pywin32 requests --quiet
if %errorLevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo [OK] Dependencies installed successfully
echo.

:: Install Windows service
echo Installing Windows service...
python install_service.py install
if %errorLevel% neq 0 (
    echo ERROR: Failed to install service
    pause
    exit /b 1
)

echo [OK] Service installed successfully
echo.

:: Create desktop shortcut
echo Creating desktop shortcut...
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Silent Print Manager.lnk'); $Shortcut.TargetPath = '%CD%\print_manager.html'; $Shortcut.WorkingDirectory = '%CD%'; $Shortcut.Description = 'Silent Print Service Management'; $Shortcut.Save()}"

echo.
echo SUCCESS: Auto installation completed!
echo.
echo Service Information:
echo   Service Name: SilentPrintService
echo   API Address: http://localhost:8888
echo   Management UI: print_manager.html
echo   Desktop Shortcut: Silent Print Manager
echo.
echo Next Steps:
echo   1. Reload Chrome extension
echo   2. Extension will auto-detect local service
echo   3. Select printer and test printing
echo   4. Enjoy true silent printing!
echo.

set /p choice="Open management interface now? (Y/N): "
if /i "%choice%"=="Y" (
    start print_manager.html
)

echo.
echo Installation completed! You can now enjoy silent printing!
pause
goto :eof

:refresh_env
:: Refresh environment variables
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set "SysPath=%%b"
for /f "tokens=2*" %%a in ('reg query "HKCU\Environment" /v PATH 2^>nul') do set "UserPath=%%b"
set "PATH=%SysPath%;%UserPath%"
goto :eof
