# 静默打印服务安装说明

## 🎯 功能特点

✅ **真正的静默打印** - 无需用户确认，直接打印  
✅ **打印机配置** - 支持选择和管理多台打印机  
✅ **Web API接口** - 提供HTTP API供浏览器扩展调用  
✅ **Windows服务** - 后台运行，开机自启  
✅ **管理界面** - 提供Web管理界面  

## 📋 系统要求

- Windows 10/11 或 Windows Server 2016+
- Python 3.7+
- 管理员权限（安装时需要）

## 🚀 安装步骤

### 1. 安装Python依赖

```bash
# 以管理员身份运行命令提示符
pip install pywin32
```

### 2. 安装Windows服务

```bash
# 在项目目录中以管理员身份运行
python install_service.py install
```

### 3. 验证服务状态

```bash
# 检查服务是否运行
python install_service.py status
```

或者打开 `print_manager.html` 查看服务状态。

### 4. 配置浏览器扩展

1. 重新加载Chrome扩展
2. 扩展会自动检测本地服务
3. 选择打印机并测试打印

## 🛠️ 服务管理

```bash
# 启动服务
python install_service.py start

# 停止服务
python install_service.py stop

# 重启服务
python install_service.py restart

# 卸载服务
python install_service.py uninstall
```

## 📖 API接口

服务运行在 `http://localhost:8888`

### GET /status
获取服务状态
```json
{
  "success": true,
  "status": "running",
  "version": "1.0.0"
}
```

### GET /printers
获取打印机列表
```json
{
  "success": true,
  "printers": [
    {
      "name": "HP LaserJet Pro",
      "server": "Local",
      "is_default": true,
      "status": "Ready"
    }
  ]
}
```

### POST /print
打印HTML内容
```json
{
  "html": "<html>...</html>",
  "printer": "HP LaserJet Pro",
  "copies": 1
}
```

### POST /set-default-printer
设置默认打印机
```json
{
  "printer": "HP LaserJet Pro"
}
```

## 🎮 使用方法

### 1. 通过浏览器扩展
- 点击扩展图标
- 选择打印机
- 点击"打印当前页面"
- **无需确认，直接静默打印！**

### 2. 通过Web API
```javascript
// 静默打印当前页面
fetch('http://localhost:8888/print', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    html: document.documentElement.outerHTML,
    printer: 'HP LaserJet Pro'
  })
});
```

### 3. 通过管理界面
- 打开 `print_manager.html`
- 管理打印机设置
- 测试打印功能

## 🔧 故障排除

### 服务无法启动
1. 确保以管理员权限运行
2. 检查Python和依赖是否正确安装
3. 查看日志文件：`C:/temp/silent_print_service.log`

### 扩展无法连接服务
1. 确认服务正在运行
2. 检查防火墙设置
3. 确认端口8888未被占用

### 打印失败
1. 确认打印机已正确安装
2. 检查打印机状态
3. 尝试使用管理界面测试

## 🔒 安全说明

- 服务仅监听本地地址 (localhost:8888)
- 不接受外部网络连接
- 建议在企业内网环境使用
- 可以通过防火墙进一步限制访问

## 📝 日志文件

服务日志保存在：`C:/temp/silent_print_service.log`

## 🆘 技术支持

如果遇到问题，请检查：
1. 日志文件中的错误信息
2. Windows事件查看器中的服务日志
3. 确保所有依赖正确安装

---

**🎉 恭喜！现在你拥有了真正的静默打印功能！**
