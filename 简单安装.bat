@echo off
:: 切换到脚本所在目录
cd /d "%~dp0"

echo.
echo ========================================
echo        静默打印服务 - 简单安装
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 错误：需要管理员权限！
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo [成功] 管理员权限验证通过
echo 当前目录：%CD%
echo.

:: 检查Python
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo 错误：未找到Python！
    echo 请先安装Python 3.7+
    echo 下载地址：https://www.python.org/downloads/
    echo.
    echo 或者使用 "一键安装.bat" 自动安装Python
    pause
    exit /b 1
)

echo [成功] Python环境检查通过
for /f "tokens=*" %%i in ('python --version 2^>^&1') do echo       版本：%%i
echo.

:: 检查必要文件
if not exist "install_service.py" (
    echo 错误：未找到 install_service.py 文件！
    echo 当前目录：%CD%
    dir *.py
    pause
    exit /b 1
)

if not exist "SilentPrintService.py" (
    echo 错误：未找到 SilentPrintService.py 文件！
    echo 当前目录：%CD%
    dir *.py
    pause
    exit /b 1
)

echo [成功] 必要文件检查通过
echo.

:: 安装依赖
echo [步骤 1/2] 正在安装Python依赖...
python -m pip install --upgrade pip --quiet
python -m pip install pywin32 requests --quiet
if %errorLevel% neq 0 (
    echo 错误：依赖安装失败！
    pause
    exit /b 1
)

echo [成功] 依赖安装完成
echo.

:: 安装服务
echo [步骤 2/2] 正在安装Windows服务...
echo 执行命令：python install_service.py install
echo.

python install_service.py install
if %errorLevel% neq 0 (
    echo.
    echo 错误：服务安装失败！
    echo 请检查错误信息并重试
    pause
    exit /b 1
)

echo.
echo ========================================
echo           安装成功完成！
echo ========================================
echo.
echo 服务信息：
echo    服务名称：SilentPrintService
echo    API地址：http://localhost:8888
echo    管理界面：print_manager.html
echo.
echo 下一步操作：
echo    1. 重新加载Chrome扩展
echo    2. 扩展会自动检测本地服务
echo    3. 选择打印机并测试打印
echo    4. 享受真正的静默打印！
echo.

set /p choice="是否立即打开管理界面？(Y/N): "
if /i "%choice%"=="Y" (
    start print_manager.html
)

echo.
echo 安装完成！
pause
