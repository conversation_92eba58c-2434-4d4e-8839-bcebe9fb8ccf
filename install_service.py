#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
静默打印服务安装脚本
"""

import sys
import os
import subprocess
import win32serviceutil
from SilentPrintService import SilentPrintService

def install_dependencies():
    """安装必要的依赖"""
    print("正在安装依赖包...")
    dependencies = [
        'pywin32',
        'requests'
    ]
    
    for dep in dependencies:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', dep])
            print(f"✅ {dep} 安装成功")
        except subprocess.CalledProcessError:
            print(f"❌ {dep} 安装失败")
            return False
    
    return True

def install_service():
    """安装Windows服务"""
    try:
        print("正在安装静默打印服务...")
        
        # 安装服务
        win32serviceutil.InstallService(
            SilentPrintService._svc_reg_class_,
            SilentPrintService._svc_name_,
            SilentPrintService._svc_display_name_,
            description=SilentPrintService._svc_description_
        )
        
        print("✅ 服务安装成功")
        
        # 启动服务
        print("正在启动服务...")
        win32serviceutil.StartService(SilentPrintService._svc_name_)
        print("✅ 服务启动成功")
        
        print("\n🎉 静默打印服务安装完成！")
        print("📋 服务信息:")
        print(f"   服务名称: {SilentPrintService._svc_name_}")
        print(f"   显示名称: {SilentPrintService._svc_display_name_}")
        print(f"   API地址: http://localhost:8888")
        print("\n📖 API接口:")
        print("   GET  /status     - 服务状态")
        print("   GET  /printers   - 获取打印机列表")
        print("   POST /print      - 打印HTML内容")
        print("   POST /set-default-printer - 设置默认打印机")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务安装失败: {e}")
        return False

def uninstall_service():
    """卸载Windows服务"""
    try:
        print("正在卸载静默打印服务...")
        
        # 停止服务
        try:
            win32serviceutil.StopService(SilentPrintService._svc_name_)
            print("✅ 服务已停止")
        except:
            pass
        
        # 卸载服务
        win32serviceutil.RemoveService(SilentPrintService._svc_name_)
        print("✅ 服务卸载成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务卸载失败: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("静默打印服务管理工具")
        print("\n用法:")
        print("  python install_service.py install   - 安装服务")
        print("  python install_service.py uninstall - 卸载服务")
        print("  python install_service.py start     - 启动服务")
        print("  python install_service.py stop      - 停止服务")
        print("  python install_service.py restart   - 重启服务")
        return
    
    command = sys.argv[1].lower()
    
    if command == 'install':
        # 检查管理员权限
        try:
            import ctypes
            if not ctypes.windll.shell32.IsUserAnAdmin():
                print("❌ 需要管理员权限来安装服务")
                print("请以管理员身份运行此脚本")
                return
        except:
            pass
        
        # 安装依赖
        if not install_dependencies():
            print("❌ 依赖安装失败，无法继续")
            return
        
        # 安装服务
        install_service()
    
    elif command == 'uninstall':
        uninstall_service()
    
    elif command == 'start':
        try:
            win32serviceutil.StartService(SilentPrintService._svc_name_)
            print("✅ 服务启动成功")
        except Exception as e:
            print(f"❌ 服务启动失败: {e}")
    
    elif command == 'stop':
        try:
            win32serviceutil.StopService(SilentPrintService._svc_name_)
            print("✅ 服务停止成功")
        except Exception as e:
            print(f"❌ 服务停止失败: {e}")
    
    elif command == 'restart':
        try:
            win32serviceutil.RestartService(SilentPrintService._svc_name_)
            print("✅ 服务重启成功")
        except Exception as e:
            print(f"❌ 服务重启失败: {e}")
    
    else:
        print(f"❌ 未知命令: {command}")

if __name__ == '__main__':
    main()
