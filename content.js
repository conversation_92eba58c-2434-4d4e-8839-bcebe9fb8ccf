// 监听来自弹出窗口的打印请求
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "print") {
    const printerType = request.printerType || 'default';

    try {
      // 根据选择的打印机类型进行不同处理
      switch(printerType) {
        case 'manual':
          // 手动选择打印机 - 打开打印对话框
          window.print();
          break;

        case 'pdf':
          // 打印到PDF
          const printCSS = `
            @media print {
              @page { margin: 0.5in; }
              body { font-size: 12pt; }
            }
          `;
          const style = document.createElement('style');
          style.textContent = printCSS;
          document.head.appendChild(style);
          window.print();
          document.head.removeChild(style);
          break;

        case 'default':
        default:
          // 使用默认打印机
          window.print();
          break;
      }

      sendResponse({success: true, message: '打印请求已发送'});
    } catch (error) {
      console.error('打印错误:', error);
      sendResponse({success: false, error: error.message});
    }
  }
  return true; // 保持消息通道开放
});

// 提供静默打印API供网页调用
window.silentPrint = function(printerType = 'default') {
  chrome.runtime.sendMessage({
    action: "print",
    printerType: printerType
  });
};

// 注入一个全局函数供网页调用
if (!window.silentPrinterInjected) {
  window.silentPrinterInjected = true;
  console.log('Silent Printer extension loaded');

  // 添加一个快捷打印按钮到页面（可选）
  const printButton = document.createElement('div');
  printButton.innerHTML = '🖨️';
  printButton.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
    background: #4CAF50;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10000;
    font-size: 18px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
  `;
  printButton.title = '快速打印';
  printButton.onclick = () => window.silentPrint();
  document.body.appendChild(printButton);
}