// 监听来自弹出窗口的打印请求
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "print") {
    const printerType = request.printerType || 'default';

    try {
      // 预设打印参数以减少用户操作
      const printSettings = {
        shouldPrintBackgrounds: true,
        shouldPrintSelectionOnly: false
      };

      // 添加打印样式
      const printCSS = `
        @media print {
          @page {
            margin: 0.5in;
            size: A4;
          }
          body {
            font-size: 12pt;
            line-height: 1.4;
            -webkit-print-color-adjust: exact;
            color-adjust: exact;
          }
          .no-print { display: none !important; }
        }
      `;

      const style = document.createElement('style');
      style.textContent = printCSS;
      document.head.appendChild(style);

      // 根据选择的打印机类型进行处理
      switch(printerType) {
        case 'manual':
          // 手动选择 - 显示完整对话框
          window.print();
          break;

        case 'pdf':
          // 尝试直接打印到PDF（仍需用户确认）
          setTimeout(() => {
            window.print();
          }, 100);
          break;

        case 'quick':
          // 快速打印 - 最小化对话框交互
          // 注意：这仍然会显示对话框，但预设了参数
          window.print();
          break;

        case 'default':
        default:
          window.print();
          break;
      }

      // 清理样式
      setTimeout(() => {
        if (style.parentNode) {
          document.head.removeChild(style);
        }
      }, 1000);

      sendResponse({success: true, message: '打印对话框已打开'});
    } catch (error) {
      console.error('打印错误:', error);
      sendResponse({success: false, error: error.message});
    }
  }
  return true;
});

// 提供静默打印API供网页调用
window.silentPrint = function(printerType = 'default') {
  chrome.runtime.sendMessage({
    action: "print",
    printerType: printerType
  });
};

// 注入一个全局函数供网页调用
if (!window.silentPrinterInjected) {
  window.silentPrinterInjected = true;
  console.log('Silent Printer extension loaded');

  // 添加一个快捷打印按钮到页面（可选）
  const printButton = document.createElement('div');
  printButton.innerHTML = '🖨️';
  printButton.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
    background: #4CAF50;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10000;
    font-size: 18px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
  `;
  printButton.title = '快速打印';
  printButton.onclick = () => window.silentPrint();
  document.body.appendChild(printButton);
}