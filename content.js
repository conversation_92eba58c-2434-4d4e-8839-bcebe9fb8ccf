// 监听来自弹出窗口的打印请求
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "print") {
    const printerType = request.printerType || 'default';

    try {
      // 预设打印参数以减少用户操作
      const printSettings = {
        shouldPrintBackgrounds: true,
        shouldPrintSelectionOnly: false
      };

      // 添加打印样式
      const printCSS = `
        @media print {
          @page {
            margin: 0.5in;
            size: A4;
          }
          body {
            font-size: 12pt;
            line-height: 1.4;
            -webkit-print-color-adjust: exact;
            color-adjust: exact;
          }
          .no-print { display: none !important; }
        }
      `;

      const style = document.createElement('style');
      style.textContent = printCSS;
      document.head.appendChild(style);

      // ⚠️ 重要说明：以下所有方法都会弹出打印对话框
      // 这是浏览器的安全限制，无法绕过！

      console.warn('🚫 浏览器安全限制：无法实现真正的静默打印！');
      console.warn('📋 所有打印操作都需要用户在对话框中确认');

      switch(printerType) {
        case 'manual':
          // ❌ 仍会弹出对话框 - 手动选择打印机
          console.log('打开打印对话框 - 手动选择模式');
          window.print();
          break;

        case 'pdf':
          // ❌ 仍会弹出对话框 - PDF打印
          console.log('打开打印对话框 - PDF模式');
          setTimeout(() => {
            window.print();
          }, 100);
          break;

        case 'quick':
          // ❌ 仍会弹出对话框 - 快速打印
          console.log('打开打印对话框 - 快速模式');
          window.print();
          break;

        case 'default':
        default:
          // ❌ 仍会弹出对话框 - 默认打印
          console.log('打开打印对话框 - 默认模式');
          window.print();
          break;
      }

      // 清理样式
      setTimeout(() => {
        if (style.parentNode) {
          document.head.removeChild(style);
        }
      }, 1000);

      sendResponse({success: true, message: '打印对话框已打开'});
    } catch (error) {
      console.error('打印错误:', error);
      sendResponse({success: false, error: error.message});
    }
  }
  return true;
});

// 提供静默打印API供网页调用
window.silentPrint = function(printerType = 'default') {
  chrome.runtime.sendMessage({
    action: "print",
    printerType: printerType
  });
};

// 注入一个全局函数供网页调用
if (!window.silentPrinterInjected) {
  window.silentPrinterInjected = true;
  console.log('Silent Printer extension loaded');

  // 添加一个快捷打印按钮到页面（可选）
  const printButton = document.createElement('div');
  printButton.innerHTML = '🖨️';
  printButton.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
    background: #4CAF50;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10000;
    font-size: 18px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
  `;
  printButton.title = '快速打印';
  printButton.onclick = () => window.silentPrint();
  document.body.appendChild(printButton);
}