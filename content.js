// 监听来自弹出窗口的打印请求
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "print") {
    // 简化版本：直接调用浏览器打印
    window.print();
    sendResponse({success: true});
  }
});

// 提供静默打印API供网页调用
window.silentPrint = function() {
  window.print();
};

// 注入一个全局函数供网页调用
if (!window.silentPrinterInjected) {
  window.silentPrinterInjected = true;
  console.log('Silent Printer extension loaded');
}