// 监听来自弹出窗口的打印请求
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "print") {
    // 将当前页面转换为PDF数据
    const printWindow = window.open('', '_blank');
    printWindow.document.write(document.documentElement.outerHTML);
    printWindow.document.close();
    
    // 使用html2pdf或类似库将内容转换为PDF
    // 这里简化处理，实际应用中需要使用适当的库
    setTimeout(() => {
      printWindow.print();
      // 在实际应用中，你需要获取PDF数据并发送给后台脚本
      // chrome.runtime.sendMessage({
      //   action: "performPrint",
      //   documentData: pdfData
      // }, response => {
      //   console.log("Print response:", response);
      // });
      printWindow.close();
    }, 1000);
  }
});

// 提供静默打印API供网页调用
window.silentPrint = function() {
  chrome.runtime.sendMessage({action: "print"});
};