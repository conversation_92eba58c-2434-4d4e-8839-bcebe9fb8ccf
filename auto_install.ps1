# 静默打印服务 - 全自动安装脚本
# Silent Print Service - Fully Automated Installer

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    静默打印服务 - 全自动安装" -ForegroundColor Cyan
Write-Host "    Silent Print Service - Auto Installer" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ 需要管理员权限！" -ForegroundColor Red
    Write-Host "ERROR: Administrator privileges required!" -ForegroundColor Red
    Write-Host "请右键点击此文件，选择'以管理员身份运行'" -ForegroundColor Yellow
    Read-Host "按回车键退出 (Press Enter to exit)"
    exit 1
}

Write-Host "✅ 管理员权限检查通过" -ForegroundColor Green
Write-Host ""

# 检查Python是否已安装
$pythonInstalled = $false
try {
    $pythonVersion = python --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 发现已安装的Python: $pythonVersion" -ForegroundColor Green
        $pythonInstalled = $true
    }
} catch {
    Write-Host "⚠️ 未发现Python安装" -ForegroundColor Yellow
}

# 如果Python未安装，自动下载安装
if (-not $pythonInstalled) {
    Write-Host "🔄 正在自动下载安装Python..." -ForegroundColor Yellow
    Write-Host "Auto-downloading and installing Python..." -ForegroundColor Yellow
    
    # Python下载URL（最新稳定版）
    $pythonUrl = "https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe"
    $pythonInstaller = "$env:TEMP\python-installer.exe"
    
    try {
        # 下载Python安装程序
        Write-Host "📥 正在下载Python安装程序..." -ForegroundColor Yellow
        Invoke-WebRequest -Uri $pythonUrl -OutFile $pythonInstaller -UseBasicParsing
        
        if (Test-Path $pythonInstaller) {
            Write-Host "✅ Python安装程序下载完成" -ForegroundColor Green
            
            # 静默安装Python
            Write-Host "🔧 正在安装Python（这可能需要几分钟）..." -ForegroundColor Yellow
            $installArgs = @(
                "/quiet",
                "InstallAllUsers=1",
                "PrependPath=1",
                "Include_test=0",
                "Include_pip=1"
            )
            
            Start-Process -FilePath $pythonInstaller -ArgumentList $installArgs -Wait
            
            # 刷新环境变量
            $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
            
            # 验证安装
            Start-Sleep -Seconds 3
            try {
                $pythonVersion = python --version 2>$null
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "✅ Python安装成功: $pythonVersion" -ForegroundColor Green
                    $pythonInstalled = $true
                } else {
                    throw "Python installation verification failed"
                }
            } catch {
                Write-Host "❌ Python安装验证失败，请手动安装" -ForegroundColor Red
                Write-Host "请访问: https://www.python.org/downloads/" -ForegroundColor Yellow
                Read-Host "按回车键退出"
                exit 1
            }
            
            # 清理安装文件
            Remove-Item $pythonInstaller -Force -ErrorAction SilentlyContinue
            
        } else {
            throw "Failed to download Python installer"
        }
        
    } catch {
        Write-Host "❌ Python自动安装失败: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "请手动下载安装Python: https://www.python.org/downloads/" -ForegroundColor Yellow
        Read-Host "按回车键退出"
        exit 1
    }
}

Write-Host ""

# 升级pip
Write-Host "🔄 正在升级pip..." -ForegroundColor Yellow
try {
    python -m pip install --upgrade pip --quiet
    Write-Host "✅ pip升级完成" -ForegroundColor Green
} catch {
    Write-Host "⚠️ pip升级失败，继续安装..." -ForegroundColor Yellow
}

Write-Host ""

# 安装Python依赖
Write-Host "🔄 正在安装Python依赖..." -ForegroundColor Yellow
Write-Host "Installing Python dependencies..." -ForegroundColor Yellow

$dependencies = @("pywin32", "requests")
$allInstalled = $true

foreach ($dep in $dependencies) {
    try {
        Write-Host "   安装 $dep..." -ForegroundColor Cyan
        python -m pip install $dep --quiet
        Write-Host "   ✅ $dep 安装成功" -ForegroundColor Green
    } catch {
        Write-Host "   ❌ $dep 安装失败" -ForegroundColor Red
        $allInstalled = $false
    }
}

if (-not $allInstalled) {
    Write-Host "❌ 部分依赖安装失败！" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

Write-Host "✅ 所有依赖安装成功" -ForegroundColor Green
Write-Host ""

# 安装Windows服务
Write-Host "🔄 正在安装Windows服务..." -ForegroundColor Yellow
Write-Host "Installing Windows service..." -ForegroundColor Yellow

try {
    python install_service.py install
    if ($LASTEXITCODE -ne 0) {
        throw "Service installation failed"
    }
    Write-Host "✅ 服务安装成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 服务安装失败！" -ForegroundColor Red
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

Write-Host ""

# 创建桌面快捷方式
Write-Host "🔄 正在创建桌面快捷方式..." -ForegroundColor Yellow
try {
    $WshShell = New-Object -comObject WScript.Shell
    $Shortcut = $WshShell.CreateShortcut("$([Environment]::GetFolderPath('Desktop'))\静默打印管理器.lnk")
    $Shortcut.TargetPath = "$PWD\print_manager.html"
    $Shortcut.WorkingDirectory = $PWD
    $Shortcut.Description = "静默打印服务管理界面"
    $Shortcut.Save()
    Write-Host "✅ 桌面快捷方式创建成功" -ForegroundColor Green
} catch {
    Write-Host "⚠️ 桌面快捷方式创建失败" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 全自动安装完成！" -ForegroundColor Green
Write-Host "SUCCESS: Fully automated installation completed!" -ForegroundColor Green
Write-Host ""

Write-Host "📋 服务信息 (Service Information):" -ForegroundColor Cyan
Write-Host "   服务名称: SilentPrintService" -ForegroundColor White
Write-Host "   API地址: http://localhost:8888" -ForegroundColor White
Write-Host "   管理界面: print_manager.html" -ForegroundColor White
Write-Host "   桌面快捷方式: 静默打印管理器" -ForegroundColor White
Write-Host ""

Write-Host "📖 下一步操作:" -ForegroundColor Cyan
Write-Host "   1. 重新加载Chrome扩展" -ForegroundColor White
Write-Host "   2. 扩展会自动检测本地服务" -ForegroundColor White
Write-Host "   3. 选择打印机并测试打印" -ForegroundColor White
Write-Host "   4. 享受真正的静默打印！" -ForegroundColor White
Write-Host ""

# 询问是否打开管理界面
$choice = Read-Host "是否立即打开管理界面？(Open management interface now?) (Y/N)"
if ($choice -eq "Y" -or $choice -eq "y") {
    Start-Process "print_manager.html"
}

Write-Host ""
Write-Host "🎊 安装完成！现在可以享受静默打印了！" -ForegroundColor Green
Write-Host "Installation completed! You can now enjoy silent printing!" -ForegroundColor Green
Read-Host "按回车键退出 (Press Enter to exit)"
