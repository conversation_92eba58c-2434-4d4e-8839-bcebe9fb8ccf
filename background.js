chrome.runtime.onInstalled.addListener(() => {
  console.log('Silent Printer extension installed');
});

// 当扩展图标被点击时注入内容脚本（如果没有弹窗的话）
// 由于我们有弹窗，这个可能不会被触发
chrome.action.onClicked.addListener((tab) => {
  chrome.scripting.executeScript({
    target: {tabId: tab.id},
    files: ['content.js']
  });
});

// 处理来自内容脚本的打印请求
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "performPrint") {
    chrome.storage.sync.get('selectedPrinter', function(data) {
      if (data.selectedPrinter) {
        chrome.printing.submitJob({
          printerId: data.selectedPrinter,
          title: "Silent Print",
          ticket: {
            pageRange: {from: 0, to: 0},
            copies: 1,
            color: {type: "color"},
            duplex: {type: "long-edge"},
            collate: true
          },
          contentType: "application/pdf",
          document: request.documentData
        }).then(jobInfo => {
          console.log("Print job submitted:", jobInfo);
          sendResponse({success: true, jobId: jobInfo.jobId});
        }).catch(error => {
          console.error("Print error:", error);
          sendResponse({success: false, error: error.message});
        });
      } else {
        sendResponse({success: false, error: "No printer selected"});
      }
    });
    return true; // 保持消息通道开放以进行异步响应
  }
});