chrome.runtime.onInstalled.addListener(() => {
  console.log('Silent Printer extension installed');
});

// 当扩展图标被点击时注入内容脚本（如果没有弹窗的话）
// 由于我们有弹窗，这个可能不会被触发
chrome.action.onClicked.addListener((tab) => {
  chrome.scripting.executeScript({
    target: {tabId: tab.id},
    files: ['content.js']
  });
});

// 处理来自内容脚本的消息
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  // 目前简化版本，主要处理打印请求的响应
  if (request.action === "print") {
    console.log("Print request received");
    sendResponse({success: true});
  }
  return true;
});