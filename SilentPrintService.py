#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
静默打印Windows服务
支持Web API调用，实现真正的静默打印
"""

import win32serviceutil
import win32service
import win32event
import servicemanager
import socket
import sys
import os
import json
import threading
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import tempfile
import subprocess
import win32print
import win32api
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('C:/temp/silent_print_service.log'),
        logging.StreamHandler()
    ]
)

class PrinterManager:
    """打印机管理类"""
    
    @staticmethod
    def get_printers():
        """获取所有可用打印机"""
        try:
            printers = []
            default_printer = win32print.GetDefaultPrinter()
            
            for printer in win32print.EnumPrinters(2):
                printer_info = {
                    'name': printer[2],
                    'server': printer[1] or 'Local',
                    'is_default': printer[2] == default_printer,
                    'status': 'Ready'  # 简化状态
                }
                printers.append(printer_info)
            
            return printers
        except Exception as e:
            logging.error(f"获取打印机列表失败: {e}")
            return []
    
    @staticmethod
    def set_default_printer(printer_name):
        """设置默认打印机"""
        try:
            win32print.SetDefaultPrinter(printer_name)
            return True
        except Exception as e:
            logging.error(f"设置默认打印机失败: {e}")
            return False
    
    @staticmethod
    def print_html(html_content, printer_name=None, copies=1):
        """打印HTML内容"""
        try:
            # 创建临时HTML文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_content)
                temp_file = f.name
            
            # 使用指定打印机或默认打印机
            if printer_name:
                # 临时设置为默认打印机
                original_default = win32print.GetDefaultPrinter()
                win32print.SetDefaultPrinter(printer_name)
            
            # 静默打印HTML文件
            for _ in range(copies):
                win32api.ShellExecute(
                    0,
                    "print",
                    temp_file,
                    None,
                    ".",
                    0  # SW_HIDE - 隐藏窗口
                )
            
            # 恢复原默认打印机
            if printer_name and 'original_default' in locals():
                win32print.SetDefaultPrinter(original_default)
            
            # 延迟删除临时文件
            threading.Timer(5.0, lambda: os.unlink(temp_file)).start()
            
            return True
        except Exception as e:
            logging.error(f"打印失败: {e}")
            return False

class PrintRequestHandler(BaseHTTPRequestHandler):
    """HTTP请求处理器"""
    
    def do_OPTIONS(self):
        """处理CORS预检请求"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def do_GET(self):
        """处理GET请求"""
        try:
            parsed_url = urlparse(self.path)
            path = parsed_url.path
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            if path == '/printers':
                # 获取打印机列表
                printers = PrinterManager.get_printers()
                response = {
                    'success': True,
                    'printers': printers
                }
            elif path == '/status':
                # 服务状态
                response = {
                    'success': True,
                    'status': 'running',
                    'version': '1.0.0'
                }
            else:
                response = {
                    'success': False,
                    'error': 'Unknown endpoint'
                }
            
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            
        except Exception as e:
            logging.error(f"GET请求处理失败: {e}")
            self.send_error(500, str(e))
    
    def do_POST(self):
        """处理POST请求"""
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            parsed_url = urlparse(self.path)
            path = parsed_url.path
            
            if path == '/print':
                # 打印请求
                html_content = data.get('html', '')
                printer_name = data.get('printer')
                copies = data.get('copies', 1)
                
                if not html_content:
                    response = {
                        'success': False,
                        'error': 'HTML content is required'
                    }
                else:
                    success = PrinterManager.print_html(html_content, printer_name, copies)
                    response = {
                        'success': success,
                        'message': '打印任务已提交' if success else '打印失败'
                    }
            
            elif path == '/set-default-printer':
                # 设置默认打印机
                printer_name = data.get('printer')
                if printer_name:
                    success = PrinterManager.set_default_printer(printer_name)
                    response = {
                        'success': success,
                        'message': '默认打印机已设置' if success else '设置失败'
                    }
                else:
                    response = {
                        'success': False,
                        'error': 'Printer name is required'
                    }
            
            else:
                response = {
                    'success': False,
                    'error': 'Unknown endpoint'
                }
            
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            
        except Exception as e:
            logging.error(f"POST请求处理失败: {e}")
            self.send_error(500, str(e))
    
    def log_message(self, format, *args):
        """重写日志方法"""
        logging.info(f"{self.address_string()} - {format % args}")

class SilentPrintService(win32serviceutil.ServiceFramework):
    """静默打印Windows服务"""
    
    _svc_name_ = "SilentPrintService"
    _svc_display_name_ = "Silent Print Service"
    _svc_description_ = "提供Web API接口的静默打印服务"
    
    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.server = None
        self.server_thread = None
    
    def SvcStop(self):
        """停止服务"""
        logging.info("正在停止静默打印服务...")
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        
        if self.server:
            self.server.shutdown()
        
        win32event.SetEvent(self.hWaitStop)
    
    def SvcDoRun(self):
        """运行服务"""
        logging.info("启动静默打印服务...")
        servicemanager.LogMsg(
            servicemanager.EVENTLOG_INFORMATION_TYPE,
            servicemanager.PYS_SERVICE_STARTED,
            (self._svc_name_, '')
        )
        
        try:
            # 启动HTTP服务器
            self.start_http_server()
            
            # 等待停止信号
            win32event.WaitForSingleObject(self.hWaitStop, win32event.INFINITE)
            
        except Exception as e:
            logging.error(f"服务运行失败: {e}")
            servicemanager.LogErrorMsg(f"服务运行失败: {e}")
    
    def start_http_server(self):
        """启动HTTP服务器"""
        try:
            port = 8888
            self.server = HTTPServer(('localhost', port), PrintRequestHandler)
            self.server_thread = threading.Thread(target=self.server.serve_forever)
            self.server_thread.daemon = True
            self.server_thread.start()
            
            logging.info(f"HTTP服务器已启动，监听端口: {port}")
            
        except Exception as e:
            logging.error(f"启动HTTP服务器失败: {e}")
            raise

if __name__ == '__main__':
    if len(sys.argv) == 1:
        servicemanager.Initialize()
        servicemanager.PrepareToHostSingle(SilentPrintService)
        servicemanager.StartServiceCtrlDispatcher()
    else:
        win32serviceutil.HandleCommandLine(SilentPrintService)
