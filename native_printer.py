#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import json
import struct
import subprocess
import tempfile
import os
from pathlib import Path

def send_message(message):
    """发送消息到Chrome扩展"""
    encoded_message = json.dumps(message).encode('utf-8')
    sys.stdout.buffer.write(struct.pack('I', len(encoded_message)))
    sys.stdout.buffer.write(encoded_message)
    sys.stdout.buffer.flush()

def read_message():
    """从Chrome扩展读取消息"""
    raw_length = sys.stdin.buffer.read(4)
    if len(raw_length) == 0:
        return None
    message_length = struct.unpack('I', raw_length)[0]
    message = sys.stdin.buffer.read(message_length).decode('utf-8')
    return json.loads(message)

def get_printers():
    """获取系统打印机列表"""
    try:
        if os.name == 'nt':  # Windows
            import win32print
            printers = []
            for printer in win32print.EnumPrinters(2):
                printers.append({
                    'name': printer[2],
                    'server': printer[1] or 'Local',
                    'default': printer[2] == win32print.GetDefaultPrinter()
                })
            return printers
        else:  # Linux/Mac
            result = subprocess.run(['lpstat', '-p'], capture_output=True, text=True)
            printers = []
            for line in result.stdout.split('\n'):
                if line.startswith('printer'):
                    name = line.split()[1]
                    printers.append({'name': name, 'server': 'Local', 'default': False})
            return printers
    except Exception as e:
        return [{'name': 'Default', 'server': 'Local', 'default': True}]

def print_html(html_content, printer_name=None):
    """打印HTML内容"""
    try:
        # 创建临时HTML文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
            f.write(html_content)
            temp_file = f.name
        
        if os.name == 'nt':  # Windows
            if printer_name:
                # 使用指定打印机
                cmd = f'start /min "" "{temp_file}" && timeout 3 && del "{temp_file}"'
            else:
                # 使用默认打印机
                cmd = f'start /min "" "{temp_file}" && timeout 3 && del "{temp_file}"'
            
            subprocess.run(cmd, shell=True)
        else:  # Linux/Mac
            if printer_name:
                subprocess.run(['lp', '-d', printer_name, temp_file])
            else:
                subprocess.run(['lp', temp_file])
            os.unlink(temp_file)
        
        return True
    except Exception as e:
        return False

def main():
    """主函数"""
    while True:
        try:
            message = read_message()
            if message is None:
                break
            
            action = message.get('action')
            
            if action == 'getPrinters':
                printers = get_printers()
                send_message({'success': True, 'printers': printers})
            
            elif action == 'print':
                html_content = message.get('html', '')
                printer_name = message.get('printer')
                
                success = print_html(html_content, printer_name)
                send_message({'success': success})
            
            else:
                send_message({'success': False, 'error': 'Unknown action'})
                
        except Exception as e:
            send_message({'success': False, 'error': str(e)})
            break

if __name__ == '__main__':
    main()
