<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>静默打印工具使用说明</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        code { background-color: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>静默打印工具使用说明</h1>
    
    <div class="section warning">
        <h3>⚠️ 重要说明</h3>
        <p>由于浏览器安全限制，完全的"静默"打印是不可能的。用户总是需要在打印对话框中确认打印操作。</p>
    </div>
    
    <div class="section info">
        <h3>📋 打印机选项说明</h3>
        <ul>
            <li><strong>系统默认打印机</strong>：使用系统设置的默认打印机</li>
            <li><strong>Microsoft Print to PDF</strong>：将页面保存为PDF文件</li>
            <li><strong>Microsoft XPS Document Writer</strong>：将页面保存为XPS文件</li>
            <li><strong>手动选择打印机</strong>：打开完整的打印对话框，可以选择任何可用的打印机</li>
        </ul>
    </div>
    
    <div class="section success">
        <h3>✅ 使用步骤</h3>
        <ol>
            <li>安装扩展后，点击浏览器工具栏中的扩展图标</li>
            <li>在下拉菜单中选择你想要的打印选项</li>
            <li>点击"保存设置"</li>
            <li>在任何网页上点击"打印当前页面"或页面右上角的打印按钮 🖨️</li>
            <li>在弹出的打印对话框中确认打印</li>
        </ol>
    </div>
    
    <div class="section info">
        <h3>🔧 高级功能</h3>
        <p>扩展会在每个网页的右上角添加一个绿色的打印按钮 🖨️，点击即可快速打印。</p>
        <p>网页开发者可以调用 <code>window.silentPrint()</code> 函数来触发打印。</p>
    </div>
    
    <div class="section warning">
        <h3>🔍 故障排除</h3>
        <ul>
            <li>如果看不到打印机选项，请确保系统中已安装打印机</li>
            <li>选择"手动选择打印机"可以访问所有系统打印机</li>
            <li>如果扩展不工作，请重新加载扩展或重启浏览器</li>
            <li>某些网站可能阻止扩展注入，此时可以手动按 Ctrl+P 打印</li>
        </ul>
    </div>
</body>
</html>
