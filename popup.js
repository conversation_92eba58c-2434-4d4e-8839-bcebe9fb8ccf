document.addEventListener('DOMContentLoaded', function() {
  const printerSelect = document.getElementById('printer');
  const statusDiv = document.getElementById('status');

  // 检查是否支持打印API
  if (chrome.printing && chrome.printing.getPrinters) {
    // 获取打印机列表
    chrome.printing.getPrinters().then(printers => {
      console.log('找到打印机:', printers);

      if (printers && printers.length > 0) {
        printers.forEach(printer => {
          const option = document.createElement('option');
          option.value = printer.id;
          option.textContent = printer.name;
          printerSelect.appendChild(option);
        });
        statusDiv.textContent = `找到 ${printers.length} 台打印机`;
      } else {
        statusDiv.textContent = '未找到可用的打印机';
      }

      // 加载已保存的打印机设置
      chrome.storage.sync.get('selectedPrinter', function(data) {
        if (data.selectedPrinter) {
          printerSelect.value = data.selectedPrinter;
        }
      });
    }).catch(error => {
      console.error('获取打印机失败:', error);
      statusDiv.textContent = '获取打印机列表失败: ' + error.message;
    });
  } else {
    // 如果不支持打印API，使用简化版本
    statusDiv.textContent = '浏览器不支持打印机API，将使用默认打印';
    const option = document.createElement('option');
    option.value = 'default';
    option.textContent = '默认打印机';
    printerSelect.appendChild(option);
  }
  
  // 保存打印机设置
  document.getElementById('save').addEventListener('click', function() {
    const selectedPrinter = document.getElementById('printer').value;
    if (selectedPrinter) {
      chrome.storage.sync.set({selectedPrinter: selectedPrinter}, function() {
        statusDiv.textContent = '设置已保存!';
        setTimeout(() => {
          statusDiv.textContent = '';
        }, 2000);
      });
    } else {
      statusDiv.textContent = '请先选择一台打印机';
    }
  });

  // 打印当前页面
  document.getElementById('print').addEventListener('click', function() {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      // 先注入内容脚本，然后发送打印消息
      chrome.scripting.executeScript({
        target: {tabId: tabs[0].id},
        files: ['content.js']
      }, () => {
        chrome.tabs.sendMessage(tabs[0].id, {action: "print"});
      });
    });
  });
});