document.addEventListener('DOMContentLoaded', function() {
  // 获取打印机列表
  chrome.printing.getPrinters().then(printers => {
    const printerSelect = document.getElementById('printer');
    printers.forEach(printer => {
      const option = document.createElement('option');
      option.value = printer.id;
      option.textContent = printer.name;
      printerSelect.appendChild(option);
    });
    
    // 加载已保存的打印机设置
    chrome.storage.sync.get('selectedPrinter', function(data) {
      if (data.selectedPrinter) {
        printerSelect.value = data.selectedPrinter;
      }
    });
  });
  
  // 保存打印机设置
  document.getElementById('save').addEventListener('click', function() {
    const selectedPrinter = document.getElementById('printer').value;
    chrome.storage.sync.set({selectedPrinter: selectedPrinter}, function() {
      document.getElementById('status').textContent = '设置已保存!';
      setTimeout(() => {
        document.getElementById('status').textContent = '';
      }, 2000);
    });
  });
  
  // 打印当前页面
  document.getElementById('print').addEventListener('click', function() {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {action: "print"});
    });
  });
});