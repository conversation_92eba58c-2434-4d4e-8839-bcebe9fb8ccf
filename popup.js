document.addEventListener('DOMContentLoaded', function() {
  const printerSelect = document.getElementById('printer');
  const statusDiv = document.getElementById('status');

  // 添加一些常见的打印机选项供用户选择
  const commonPrinters = [
    { name: '系统默认打印机', value: 'default' },
    { name: 'Microsoft Print to PDF', value: 'pdf' },
    { name: 'Microsoft XPS Document Writer', value: 'xps' },
    { name: '手动选择打印机', value: 'manual' }
  ];

  // 添加常见打印机选项
  commonPrinters.forEach(printer => {
    const option = document.createElement('option');
    option.value = printer.value;
    option.textContent = printer.name;
    printerSelect.appendChild(option);
  });

  // 尝试获取系统打印机（如果API可用）
  if (chrome.printing && chrome.printing.getPrinters) {
    chrome.printing.getPrinters().then(printers => {
      console.log('系统打印机:', printers);

      if (printers && printers.length > 0) {
        // 添加分隔线
        const separator = document.createElement('option');
        separator.disabled = true;
        separator.textContent = '--- 系统打印机 ---';
        printerSelect.appendChild(separator);

        // 添加系统检测到的打印机
        printers.forEach(printer => {
          const option = document.createElement('option');
          option.value = 'system_' + printer.id;
          option.textContent = printer.name + ' (系统检测)';
          printerSelect.appendChild(option);
        });
        statusDiv.textContent = `检测到 ${printers.length} 台系统打印机`;
      }
    }).catch(error => {
      console.log('无法获取系统打印机:', error);
      statusDiv.textContent = '使用通用打印选项';
    });
  } else {
    statusDiv.textContent = '使用通用打印选项';
  }

  // 加载已保存的打印机设置
  chrome.storage.sync.get('selectedPrinter', function(data) {
    if (data.selectedPrinter) {
      printerSelect.value = data.selectedPrinter;
    }
  });
  
  // 保存打印机设置
  document.getElementById('save').addEventListener('click', function() {
    const selectedPrinter = document.getElementById('printer').value;
    if (selectedPrinter) {
      chrome.storage.sync.set({selectedPrinter: selectedPrinter}, function() {
        statusDiv.textContent = '设置已保存!';
        setTimeout(() => {
          statusDiv.textContent = '';
        }, 2000);
      });
    } else {
      statusDiv.textContent = '请先选择一台打印机';
    }
  });

  // 打印当前页面
  document.getElementById('print').addEventListener('click', function() {
    const selectedPrinter = printerSelect.value;

    if (!selectedPrinter) {
      statusDiv.textContent = '请先选择打印机';
      return;
    }

    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      // 先注入内容脚本，然后发送打印消息
      chrome.scripting.executeScript({
        target: {tabId: tabs[0].id},
        files: ['content.js']
      }, () => {
        chrome.tabs.sendMessage(tabs[0].id, {
          action: "print",
          printerType: selectedPrinter
        }, (response) => {
          if (response && response.success) {
            statusDiv.textContent = '打印请求已发送';
          } else {
            statusDiv.textContent = '打印失败';
          }
        });
      });
    });
  });
});