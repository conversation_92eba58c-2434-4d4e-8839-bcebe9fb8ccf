document.addEventListener('DOMContentLoaded', function() {
  const printerSelect = document.getElementById('printer');
  const statusDiv = document.getElementById('status');
  const API_BASE = 'http://localhost:8888';

  // 检查本地服务状态并加载打印机
  async function loadPrintersFromService() {
    try {
      // 检查服务状态
      const statusResponse = await fetch(`${API_BASE}/status`);
      const statusData = await statusResponse.json();

      if (statusData.success) {
        statusDiv.textContent = '✅ 本地打印服务已连接';

        // 获取打印机列表
        const printersResponse = await fetch(`${API_BASE}/printers`);
        const printersData = await printersResponse.json();

        if (printersData.success && printersData.printers.length > 0) {
          // 清空现有选项
          printerSelect.innerHTML = '<option value="">请选择打印机...</option>';

          // 添加本地服务的打印机
          printersData.printers.forEach(printer => {
            const option = document.createElement('option');
            option.value = printer.name;
            option.textContent = printer.name + (printer.is_default ? ' (默认)' : '');
            printerSelect.appendChild(option);
          });

          statusDiv.textContent = `✅ 找到 ${printersData.printers.length} 台打印机 (本地服务)`;
        } else {
          statusDiv.textContent = '⚠️ 本地服务无打印机';
          addFallbackOptions();
        }
      } else {
        throw new Error('服务响应异常');
      }
    } catch (error) {
      console.log('本地服务不可用，使用备用选项:', error);
      statusDiv.textContent = '⚠️ 本地服务不可用，使用浏览器打印';
      addFallbackOptions();
    }
  }

  // 添加备用打印选项
  function addFallbackOptions() {
    printerSelect.innerHTML = '';
    const fallbackOptions = [
      { name: '手动选择打印机', value: 'manual' },
      { name: '系统默认打印机', value: 'default' },
      { name: 'Microsoft Print to PDF', value: 'pdf' }
    ];

    fallbackOptions.forEach(printer => {
      const option = document.createElement('option');
      option.value = printer.value;
      option.textContent = printer.name;
      printerSelect.appendChild(option);
    });
  }

  // 初始化
  loadPrintersFromService();

  // 加载已保存的打印机设置
  chrome.storage.sync.get('selectedPrinter', function(data) {
    if (data.selectedPrinter) {
      printerSelect.value = data.selectedPrinter;
    }
  });
  
  // 保存打印机设置
  document.getElementById('save').addEventListener('click', function() {
    const selectedPrinter = document.getElementById('printer').value;
    if (selectedPrinter) {
      chrome.storage.sync.set({selectedPrinter: selectedPrinter}, function() {
        statusDiv.textContent = '设置已保存!';
        setTimeout(() => {
          statusDiv.textContent = '';
        }, 2000);
      });
    } else {
      statusDiv.textContent = '请先选择一台打印机';
    }
  });

  // 打印当前页面
  document.getElementById('print').addEventListener('click', async function() {
    const selectedPrinter = printerSelect.value;

    if (!selectedPrinter) {
      statusDiv.textContent = '请先选择打印机';
      return;
    }

    statusDiv.textContent = '🔄 正在准备打印...';

    try {
      // 获取当前页面内容
      const [tab] = await chrome.tabs.query({active: true, currentWindow: true});

      // 先尝试使用本地服务进行真正的静默打印
      try {
        const response = await fetch(`${API_BASE}/status`);
        const serviceData = await response.json();

        if (serviceData.success) {
          // 本地服务可用，获取页面HTML并静默打印
          chrome.scripting.executeScript({
            target: {tabId: tab.id},
            function: getPageHTML
          }, async (results) => {
            if (results && results[0]) {
              const html = results[0].result;
              await silentPrintViaService(html, selectedPrinter);
            }
          });
          return;
        }
      } catch (error) {
        console.log('本地服务不可用，使用浏览器打印');
      }

      // 本地服务不可用，使用浏览器打印
      chrome.scripting.executeScript({
        target: {tabId: tab.id},
        files: ['content.js']
      }, () => {
        chrome.tabs.sendMessage(tab.id, {
          action: "print",
          printerType: selectedPrinter
        }, (response) => {
          if (response && response.success) {
            statusDiv.textContent = '✅ 打印对话框已打开';
          } else {
            statusDiv.textContent = '❌ 打印失败';
          }
        });
      });

    } catch (error) {
      statusDiv.textContent = `❌ 打印失败: ${error.message}`;
    }
  });

  // 获取页面HTML内容的函数
  function getPageHTML() {
    return document.documentElement.outerHTML;
  }

  // 通过本地服务进行静默打印
  async function silentPrintViaService(html, printerName) {
    try {
      const response = await fetch(`${API_BASE}/print`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          html: html,
          printer: printerName === 'default' ? null : printerName,
          copies: 1
        })
      });

      const data = await response.json();

      if (data.success) {
        statusDiv.textContent = '✅ 静默打印成功！';
      } else {
        statusDiv.textContent = `❌ 打印失败: ${data.error}`;
      }
    } catch (error) {
      statusDiv.textContent = `❌ 打印失败: ${error.message}`;
    }
  }
});