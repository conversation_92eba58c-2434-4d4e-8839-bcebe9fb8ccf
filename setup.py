#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
静默打印服务 - 自动安装脚本
如果Python已安装，可以直接运行此脚本完成所有安装
"""

import os
import sys
import subprocess
import urllib.request
import tempfile
import time
import ctypes
from pathlib import Path

def is_admin():
    """检查是否有管理员权限"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """以管理员权限重新运行"""
    if is_admin():
        return True
    else:
        print("❌ 需要管理员权限！")
        print("请以管理员身份运行此脚本")
        input("按回车键退出...")
        return False

def install_package(package):
    """安装Python包"""
    try:
        print(f"🔄 正在安装 {package}...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", package, "--quiet"
        ])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ {package} 安装失败")
        return False

def install_service():
    """安装Windows服务"""
    try:
        print("🔄 正在安装Windows服务...")
        subprocess.check_call([sys.executable, "install_service.py", "install"])
        print("✅ 服务安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 服务安装失败: {e}")
        return False

def create_desktop_shortcut():
    """创建桌面快捷方式"""
    try:
        import win32com.client
        
        desktop = Path.home() / "Desktop"
        shortcut_path = desktop / "静默打印管理器.lnk"
        
        shell = win32com.client.Dispatch("WScript.Shell")
        shortcut = shell.CreateShortCut(str(shortcut_path))
        shortcut.Targetpath = str(Path.cwd() / "print_manager.html")
        shortcut.WorkingDirectory = str(Path.cwd())
        shortcut.Description = "静默打印服务管理界面"
        shortcut.save()
        
        print("✅ 桌面快捷方式创建成功")
        return True
    except Exception as e:
        print(f"⚠️ 桌面快捷方式创建失败: {e}")
        return False

def main():
    """主安装流程"""
    print()
    print("=" * 50)
    print("    静默打印服务 - 自动安装脚本")
    print("    Silent Print Service - Auto Setup")
    print("=" * 50)
    print()
    
    # 检查管理员权限
    if not run_as_admin():
        return False
    
    print("✅ 管理员权限检查通过")
    print()
    
    # 升级pip
    print("🔄 正在升级pip...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "--upgrade", "pip", "--quiet"
        ])
        print("✅ pip升级完成")
    except:
        print("⚠️ pip升级失败，继续安装...")
    
    print()
    
    # 安装依赖包
    dependencies = ["pywin32", "requests"]
    all_installed = True
    
    for dep in dependencies:
        if not install_package(dep):
            all_installed = False
    
    if not all_installed:
        print("❌ 部分依赖安装失败！")
        input("按回车键退出...")
        return False
    
    print()
    
    # 安装Windows服务
    if not install_service():
        input("按回车键退出...")
        return False
    
    print()
    
    # 创建桌面快捷方式
    create_desktop_shortcut()
    
    print()
    print("🎉 自动安装完成！")
    print()
    print("📋 服务信息:")
    print("   服务名称: SilentPrintService")
    print("   API地址: http://localhost:8888")
    print("   管理界面: print_manager.html")
    print()
    print("📖 下一步操作:")
    print("   1. 重新加载Chrome扩展")
    print("   2. 扩展会自动检测本地服务")
    print("   3. 选择打印机并测试打印")
    print("   4. 享受真正的静默打印！")
    print()
    
    # 询问是否打开管理界面
    choice = input("是否立即打开管理界面？(Y/N): ").strip().upper()
    if choice == "Y":
        try:
            os.startfile("print_manager.html")
        except:
            print("无法自动打开管理界面，请手动打开 print_manager.html")
    
    print()
    print("🎊 安装完成！现在可以享受静默打印了！")
    input("按回车键退出...")
    return True

if __name__ == "__main__":
    main()
