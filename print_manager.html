<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>静默打印管理器</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        
        .section h3 {
            margin-top: 0;
            color: #555;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        select, input, textarea, button {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        button {
            background-color: #007cba;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
            margin-top: 10px;
        }
        
        button:hover {
            background-color: #005a87;
        }
        
        .btn-danger {
            background-color: #dc3545;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .btn-success {
            background-color: #28a745;
        }
        
        .btn-success:hover {
            background-color: #218838;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .printer-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .printer-item {
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: white;
        }
        
        .printer-item.default {
            border-color: #28a745;
            background-color: #f8fff9;
        }
        
        .printer-name {
            font-weight: bold;
            color: #333;
        }
        
        .printer-info {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        textarea {
            height: 200px;
            font-family: monospace;
        }
        
        .api-info {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖨️ 静默打印管理器</h1>
        
        <!-- 服务状态 -->
        <div class="section">
            <h3>📊 服务状态</h3>
            <div id="serviceStatus" class="status info">正在检查服务状态...</div>
            <button onclick="checkServiceStatus()">刷新状态</button>
        </div>
        
        <!-- 打印机管理 -->
        <div class="section">
            <h3>🖨️ 打印机管理</h3>
            <button onclick="loadPrinters()" class="btn-success">刷新打印机列表</button>
            <div id="printerList" class="printer-list"></div>
        </div>
        
        <!-- 测试打印 -->
        <div class="section">
            <h3>🧪 测试打印</h3>
            <div class="form-group">
                <label for="testPrinter">选择打印机:</label>
                <select id="testPrinter">
                    <option value="">使用默认打印机</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="testContent">HTML内容:</label>
                <textarea id="testContent" placeholder="输入要打印的HTML内容...">
<html>
<head>
    <title>测试打印</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
    </style>
</head>
<body>
    <h1>静默打印测试</h1>
    <p>这是一个测试打印页面。</p>
    <p>打印时间: <script>document.write(new Date().toLocaleString());</script></p>
    <p>如果您看到这个页面，说明静默打印服务工作正常！</p>
</body>
</html>
                </textarea>
            </div>
            
            <div class="form-group">
                <label for="copies">打印份数:</label>
                <input type="number" id="copies" value="1" min="1" max="10">
            </div>
            
            <button onclick="testPrint()" class="btn-success">开始打印</button>
            <div id="printStatus"></div>
        </div>
        
        <!-- API文档 -->
        <div class="section">
            <h3>📖 API文档</h3>
            <div class="api-info">
                <strong>服务地址:</strong> http://localhost:8888<br><br>
                
                <strong>GET /status</strong> - 获取服务状态<br>
                <strong>GET /printers</strong> - 获取打印机列表<br><br>
                
                <strong>POST /print</strong> - 打印HTML内容<br>
                参数: { "html": "HTML内容", "printer": "打印机名称", "copies": 1 }<br><br>
                
                <strong>POST /set-default-printer</strong> - 设置默认打印机<br>
                参数: { "printer": "打印机名称" }
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8888';
        
        // 检查服务状态
        async function checkServiceStatus() {
            const statusDiv = document.getElementById('serviceStatus');
            try {
                const response = await fetch(`${API_BASE}/status`);
                const data = await response.json();
                
                if (data.success) {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = `✅ 服务运行正常 (版本: ${data.version})`;
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = '❌ 服务响应异常';
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ 无法连接到服务: ${error.message}`;
            }
        }
        
        // 加载打印机列表
        async function loadPrinters() {
            const printerList = document.getElementById('printerList');
            const testPrinter = document.getElementById('testPrinter');
            
            try {
                const response = await fetch(`${API_BASE}/printers`);
                const data = await response.json();
                
                if (data.success) {
                    // 更新打印机列表显示
                    printerList.innerHTML = '';
                    testPrinter.innerHTML = '<option value="">使用默认打印机</option>';
                    
                    data.printers.forEach(printer => {
                        // 打印机卡片
                        const printerDiv = document.createElement('div');
                        printerDiv.className = `printer-item ${printer.is_default ? 'default' : ''}`;
                        printerDiv.innerHTML = `
                            <div class="printer-name">${printer.name} ${printer.is_default ? '(默认)' : ''}</div>
                            <div class="printer-info">
                                服务器: ${printer.server}<br>
                                状态: ${printer.status}
                            </div>
                            ${!printer.is_default ? `<button onclick="setDefaultPrinter('${printer.name}')" style="margin-top: 10px;">设为默认</button>` : ''}
                        `;
                        printerList.appendChild(printerDiv);
                        
                        // 测试打印下拉选项
                        const option = document.createElement('option');
                        option.value = printer.name;
                        option.textContent = printer.name;
                        testPrinter.appendChild(option);
                    });
                } else {
                    printerList.innerHTML = '<div class="status error">获取打印机列表失败</div>';
                }
            } catch (error) {
                printerList.innerHTML = `<div class="status error">错误: ${error.message}</div>`;
            }
        }
        
        // 设置默认打印机
        async function setDefaultPrinter(printerName) {
            try {
                const response = await fetch(`${API_BASE}/set-default-printer`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ printer: printerName })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert('✅ 默认打印机设置成功');
                    loadPrinters(); // 刷新列表
                } else {
                    alert(`❌ 设置失败: ${data.error}`);
                }
            } catch (error) {
                alert(`❌ 设置失败: ${error.message}`);
            }
        }
        
        // 测试打印
        async function testPrint() {
            const printer = document.getElementById('testPrinter').value;
            const content = document.getElementById('testContent').value;
            const copies = parseInt(document.getElementById('copies').value);
            const statusDiv = document.getElementById('printStatus');
            
            if (!content.trim()) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ 请输入HTML内容';
                return;
            }
            
            statusDiv.className = 'status info';
            statusDiv.textContent = '🔄 正在打印...';
            
            try {
                const response = await fetch(`${API_BASE}/print`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        html: content,
                        printer: printer || null,
                        copies: copies
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✅ 打印任务已提交成功';
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = `❌ 打印失败: ${data.error}`;
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ 打印失败: ${error.message}`;
            }
        }
        
        // 页面加载时初始化
        window.onload = function() {
            checkServiceStatus();
            loadPrinters();
        };
    </script>
</body>
</html>
